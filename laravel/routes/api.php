<?php

use App\Http\Controllers\LockScreenController;
use App\Services\UnifiedService;

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ExportController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/





Route::post('/lockscreenunlock', [LockScreenController::class, 'post']);


Route::get('load-distributors', function () {
        $unified = new UnifiedService();

        $distributors = $unified->getDistributors();

        return response()->json(['result' => $distributors], 200);
});

Route::get('load-bricks', function () {
        $unified = new UnifiedService();

        $bricks = $unified->getBricks();

        return response()->json(['result' => $bricks], 200);
});






// ... existing routes

// Export routes
//Route::prefix('exports')->group(function () {
//    Route::get('/download', [ExportController::class, 'downloadExportedFile']);
//    Route::post('/process-and-download', [ExportController::class, 'processAndDownload']);
//});
//
//// Direct download route for exported files
//Route::get('/download/{filename}', [ExportController::class, 'downloadFileByName'])
//    ->where('filename', '[a-f0-9\-]+\.(csv|xlsx|pdf|json)')
//    ->name('download_exported_file');


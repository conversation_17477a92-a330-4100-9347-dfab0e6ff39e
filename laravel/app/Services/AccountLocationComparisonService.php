<?php

namespace App\Services;

use App\Account;
use App\AccountLines;
use App\AccountType;
use App\ActualVisit;
use App\Line;
use App\LineDivision;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

/**
 * Service for generating location comparison reports between customer accounts and their visit locations
 *
 * This service analyzes the geographical differences between registered account locations
 * and actual visit locations to identify patterns and discrepancies in location data.
 *
 * Features:
 * - Extracts recent visit locations for each account
 * - Compares visit coordinates with registered account coordinates
 * - Calculates distance differences and identifies patterns
 * - Calculates visit frequency as unique user count within 100-meter radius
 * - Flags accounts with significant location deviations
 * - Provides summary statistics and detailed analysis
 *
 * Laravel Octane Compatible:
 * - No static state
 * - Proper dependency injection
 * - Stateless design
 */
class AccountLocationComparisonService
{
    /**
     * Distance calculation service
     */
    private DifferenceBetweenTwoCoordinates $distanceCalculator;

    /**
     * Default distance threshold in meters for flagging deviations
     */
    private const DEFAULT_DISTANCE_THRESHOLD = 1000;

    /**
     * Number of recent visits to analyze per account
     */
    private const DEFAULT_RECENT_VISITS_LIMIT = 5;

    /**
     * Radius in meters for visit frequency calculation
     */
    private const FREQUENCY_RADIUS_METERS = 100;

    /**
     * Constructor with dependency injection
     *
     * @param DifferenceBetweenTwoCoordinates $distanceCalculator
     */
    public function __construct(DifferenceBetweenTwoCoordinates $distanceCalculator)
    {
        $this->distanceCalculator = $distanceCalculator;
    }

    /**
     * Generate location comparison report for customer accounts
     *
     * @param array $options Report generation options
     * @return array Comprehensive location comparison report
     */
    public function generateLocationComparisonReport(array $options = []): array
    {
        Log::info('Starting location comparison report generation', ['options' => $options]);

        $lineIds = $options['line_ids'] ?? null;
        $divIds = $options['div_ids'] ?? null;
        $accountTypeIds = $options['account_type_ids'] ?? null;
        $locationFilter = $options['location_filter'] ?? null;
        $fromDate = $options['from_date'] ?? null;
        $toDate = $options['to_date'] ?? null;
        $distanceThreshold = $options['distance_threshold'] ?? self::DEFAULT_DISTANCE_THRESHOLD;
        $recentVisitsLimit = $options['recent_visits_limit'] ?? self::DEFAULT_RECENT_VISITS_LIMIT;

        try {
            // Get accounts to analyze based on LineDivision hierarchy or specific account IDs
            $accounts = $this->getAccountsForAnalysis($lineIds, $divIds, $accountTypeIds, $locationFilter);

            if ($accounts->isEmpty()) {
                Log::warning('No accounts found for location comparison analysis', ['line_ids' => $lineIds, 'div_ids' => $divIds]);
                return $this->buildEmptyReport();
            }

            $reportData = [];
            $summaryStats = [
                'total_accounts_analyzed' => 0,
                'accounts_with_discrepancies' => 0,
                'total_visits_analyzed' => 0,
                'average_distance_variance' => 0,
                'max_distance_variance' => 0,
                'accounts_without_registered_location' => 0,
                'accounts_without_visits' => 0
            ];

            $totalDistanceVariances = [];

            foreach ($accounts as $account) {
                Log::debug('Analyzing account location data', ['account_id' => $account->id, 'account_name' => $account->name]);

                $accountAnalysis = $this->analyzeAccountLocationData(
                    $account,
                    $fromDate,
                    $toDate,
                    $distanceThreshold,
                    $recentVisitsLimit,
                );

                if ($accountAnalysis) {
                    $reportData[] = $accountAnalysis;
                    $summaryStats['total_accounts_analyzed']++;

                    if ($accountAnalysis['has_discrepancies']) {
                        $summaryStats['accounts_with_discrepancies']++;
                    }

                    $summaryStats['total_visits_analyzed'] += $accountAnalysis['visits_count'];

                    if ($accountAnalysis['registered_location']['has_location']) {
                        foreach ($accountAnalysis['visit_locations'] as $visit) {
                            if (isset($visit['distance_from_registered'])) {
                                $totalDistanceVariances[] = $visit['distance_from_registered'];
                            }
                        }
                    } else {
                        $summaryStats['accounts_without_registered_location']++;
                    }

                    if ($accountAnalysis['visits_count'] === 0) {
                        $summaryStats['accounts_without_visits']++;
                    }
                }
            }

            // Calculate summary statistics
            if (!empty($totalDistanceVariances)) {
                $summaryStats['average_distance_variance'] = round(array_sum($totalDistanceVariances) / count($totalDistanceVariances), 2);
                $summaryStats['max_distance_variance'] = round(max($totalDistanceVariances), 2);
            }

            Log::info('Location comparison report generated successfully', [
                'accounts_analyzed' => $summaryStats['total_accounts_analyzed'],
                'accounts_with_discrepancies' => $summaryStats['accounts_with_discrepancies']
            ]);

            return [
                'success' => true,
                'summary' => $summaryStats,
                'accounts' => $reportData,
                'generated_at' => Carbon::now()->toISOString(),
                'parameters' => [
                    'line_ids' => $lineIds,
                    'div_ids' => $divIds,
                    'account_type_ids' => $accountTypeIds,
                    'from_date' => $fromDate,
                    'to_date' => $toDate,
                    'distance_threshold' => $distanceThreshold,
                    'recent_visits_limit' => $recentVisitsLimit
                ]
            ];
        } catch (\Exception $e) {
            Log::error('Error generating location comparison report', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => 'Failed to generate location comparison report: ' . $e->getMessage(),
                'generated_at' => Carbon::now()->toISOString()
            ];
        }
    }

    /**
     * Get accounts for analysis based on LineDivision hierarchy or specific account IDs
     *
     * @param array|null $lineIds Array of Line IDs for filtering by LineDivision hierarchy
     * @param array|null $divIds Array of Division IDs for filtering by specific LineDivisions
     * @param array|null $accountTypeIds Array of Account Type IDs for filtering by account types
     * @param string|null $locationFilter Filter by location presence: 'with_location' or 'without_location'
     * @return Collection Collection of Account models
     */
    private function getAccountsForAnalysis(?array $lineIds, ?array $divIds, ?array $accountTypeIds = null, ?string $locationFilter = null): Collection
    {
        $query = Account::query()
            ->select(['accounts.id', 'accounts.name', 'accounts.code', 'accounts.address', 'account_lines.ll', 'account_lines.lg'])
            ->whereNull('accounts.deleted_at')
            ->where('accounts.active_date', '<=', now())
            ->where(function ($q) {
                $q->whereNull('accounts.inactive_date')
                    ->orWhere('accounts.inactive_date', '>=', now());
            });

        // Apply AccountType filtering if provided
        if ($accountTypeIds && !empty($accountTypeIds)) {
            $query->whereIn('accounts.type_id', $accountTypeIds);
        }


        $query->join('account_lines', 'accounts.id', '=', 'account_lines.account_id')
            ->whereNull('account_lines.deleted_at')
            ->where('account_lines.from_date', '<=', now())
            ->where(function ($q) {
                $q->whereNull('account_lines.to_date')
                    ->orWhere('account_lines.to_date', '>=', now());
            });

        if ($lineIds && !empty($lineIds)) {
            $query->whereIn('account_lines.line_id', $lineIds);
        }

        if ($divIds && !empty($divIds)) {
            $query->whereIn('account_lines.line_division_id', $divIds);
        }

        // Apply location filter if provided
        if ($locationFilter === 'with_location') {
            $query->whereNotNull('account_lines.ll')
                ->whereNotNull('account_lines.lg')
                ->where('account_lines.ll', '!=', '')
                ->where('account_lines.lg', '!=', '')
                ->where('account_lines.ll', '!=', '0')
                ->where('account_lines.lg', '!=', '0');
        } elseif ($locationFilter === 'without_location') {
            $query->where(function ($q) {
                $q->whereNull('account_lines.ll')
                    ->orWhereNull('account_lines.lg')
                    ->orWhere('account_lines.ll', '=', '')
                    ->orWhere('account_lines.lg', '=', '')
                    ->orWhere('account_lines.ll', '=', '0')
                    ->orWhere('account_lines.lg', '=', '0');
            });
        }

        Log::debug('Accounts query for location comparison analysis', [
            'line_ids' => $lineIds,
            'div_ids' => $divIds,
            'account_type_ids' => $accountTypeIds,
            'location_filter' => $locationFilter
        ]);

        return $query->distinct()->get();
    }

    /**
     * Build empty report structure
     *
     * @return array Empty report structure
     */
    private function buildEmptyReport(): array
    {
        return [
            'success' => true,
            'summary' => [
                'total_accounts_analyzed' => 0,
                'accounts_with_discrepancies' => 0,
                'total_visits_analyzed' => 0,
                'average_distance_variance' => 0,
                'max_distance_variance' => 0,
                'accounts_without_registered_location' => 0,
                'accounts_without_visits' => 0
            ],
            'accounts' => [],
            'generated_at' => Carbon::now()->toISOString()
        ];
    }

    /**
     * Analyze location data for a specific account
     *
     * @param Account $account Account to analyze
     * @param string|null $fromDate Start date for visit analysis
     * @param string|null $toDate End date for visit analysis
     * @param float $distanceThreshold Distance threshold for flagging deviations
     * @param int $recentVisitsLimit Number of recent visits to analyze
     * @return array|null Account analysis data or null if no data
     */
    private function analyzeAccountLocationData(
        Account $account,
        ?string $fromDate,
        ?string $toDate,
        float $distanceThreshold,
        int $recentVisitsLimit,
    ): ?array {
        try {
            // Get registered location from AccountLines (most recent active record)
            $registeredLocation = $this->getAccountRegisteredLocation($account);

            // Get recent visit locations with LineDivision context
            $visitLocations = $this->getAccountVisitLocations($account, $fromDate, $toDate, $recentVisitsLimit);

            // Get all visit coordinates for frequency calculation (not limited by date range)
            $allVisitCoordinates = $this->getAllAccountVisitCoordinates($account);

            Log::debug('Starting visit frequency calculation', [
                'account_id' => $account->id,
                'recent_visits_count' => $visitLocations->count(),
                'total_visits_for_frequency' => $allVisitCoordinates->count()
            ]);

            $frequencyCalculationStart = microtime(true);

            // Analyze location patterns and discrepancies
            $hasDiscrepancies = false;
            $maxDistanceFromRegistered = 0;
            $visitLocationData = [];

            foreach ($visitLocations as $visit) {
                $visitData = [
                    'visit_id' => $visit->id,
                    'visit_date' => $visit->visit_date,
                    'latitude' => (float) $visit->ll,
                    'longitude' => (float) $visit->lg,
                    'visit_address' => $visit->visit_address,
                    'user' => $this->formatUserData($visit)
                ];

                // Calculate visit frequency within 100-meter radius (unique users count)
                $visitData['visit_frequency'] = $this->calculateVisitFrequency(
                    $visit->ll,
                    $visit->lg,
                    $allVisitCoordinates
                );

                // Calculate distance from registered location if both have coordinates
                if (
                    $registeredLocation['has_location'] &&
                    !empty($visit->ll) && !empty($visit->lg) &&
                    $visit->ll != 0 && $visit->lg != 0
                ) {

                    $distance = $this->distanceCalculator->distanceBetweenTwoCoordinates(
                        $visit->ll,
                        $visit->lg,
                        $registeredLocation['latitude'],
                        $registeredLocation['longitude'],
                        'M' // meters
                    );

                    $visitData['distance_from_registered'] = round($distance, 2);
                    $maxDistanceFromRegistered = max($maxDistanceFromRegistered, $distance);

                    // Check if distance exceeds threshold
                    if ($distance > $distanceThreshold) {
                        $hasDiscrepancies = true;
                        $visitData['exceeds_threshold'] = true;
                    } else {
                        $visitData['exceeds_threshold'] = false;
                    }
                } else {
                    $visitData['distance_from_registered'] = null;
                    $visitData['exceeds_threshold'] = null;
                }

                $visitLocationData[] = $visitData;
            }

            // Log performance metrics for accounts with many visits
            $frequencyCalculationEnd = microtime(true);
            $calculationTime = round(($frequencyCalculationEnd - $frequencyCalculationStart) * 1000, 2);

            if ($allVisitCoordinates->count() > 50) {
                Log::info('Visit frequency calculation performance', [
                    'account_id' => $account->id,
                    'total_visits' => $allVisitCoordinates->count(),
                    'calculation_time_ms' => $calculationTime
                ]);
            }

            Log::debug('Completed visit frequency calculation', [
                'account_id' => $account->id,
                'calculation_time_ms' => $calculationTime
            ]);

            return [
                'account_id' => $account->id,
                'account_name' => $account->name,
                'account_code' => $account->code,
                'account_address' => $account->address,
                'registered_location' => $registeredLocation,
                'visit_locations' => $visitLocationData,
                'visits_count' => count($visitLocationData),
                'has_discrepancies' => $hasDiscrepancies,
                'max_distance_from_registered' => round($maxDistanceFromRegistered, 2),
                'distance_threshold_used' => $distanceThreshold
            ];
        } catch (\Exception $e) {
            Log::error('Error analyzing account location data', [
                'account_id' => $account->id,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Get registered location for an account from AccountLines with LineDivision context
     *
     * @param Account $account Account to get location for
     * @return array Registered location data
     */
    private function getAccountRegisteredLocation(Account $account): array
    {
        try {
            // Get the most recent active AccountLines record with location data
            $query = AccountLines::select([
                "account_lines.lg",
                "account_lines.ll",
                "account_lines.from_date",
                "account_lines.to_date"
            ])->where('account_id', $account->id)
                ->where('from_date', '<=', Carbon::now())
                ->where(function ($query) {
                    $query->whereNull('to_date')
                        ->orWhere('to_date', '>=', Carbon::now());
                })
                ->whereNotNull('ll')
                ->whereNotNull('lg')
                ->where('ll', '!=', '')
                ->where('lg', '!=', '')
                ->where('ll', '!=', '0')
                ->where('lg', '!=', '0');


            $accountLine = $query->orderBy('from_date', 'desc')->first();

            if ($accountLine) {
                return [
                    'has_location' => true,
                    'latitude' => (float) $accountLine->ll,
                    'longitude' => (float) $accountLine->lg,
                    'source' => 'account_lines',
                    'from_date' => $accountLine->from_date,
                    'to_date' => $accountLine->to_date
                ];
            }

            // Fallback to Account table location if available
            if (
                !empty($account->ll) && !empty($account->lg) &&
                $account->ll != 0 && $account->lg != 0
            ) {
                return [
                    'has_location' => true,
                    'latitude' => (float) $account->ll,
                    'longitude' => (float) $account->lg,
                    'source' => 'accounts',
                    'from_date' => null,
                    'to_date' => null
                ];
            }

            return [
                'has_location' => false,
                'latitude' => null,
                'longitude' => null,
                'source' => null,
                'from_date' => null,
                'to_date' => null
            ];
        } catch (\Exception $e) {
            Log::error('Error getting account registered location', [
                'account_id' => $account->id,
                'error' => $e->getMessage()
            ]);

            return [
                'has_location' => false,
                'latitude' => null,
                'longitude' => null,
                'source' => null,
                'from_date' => null,
                'to_date' => null
            ];
        }
    }

    /**
     * Get recent visit locations for an account with LineDivision context
     *
     * @param Account $account Account to get visits for
     * @param string|null $fromDate Start date filter
     * @param string|null $toDate End date filter
     * @param int $limit Number of recent visits to retrieve
     * @return Collection Collection of ActualVisit models
     */
    private function getAccountVisitLocations(
        Account $account,
        ?string $fromDate,
        ?string $toDate,
        int $limit,
    ): Collection {
        try {
            $query = ActualVisit::where('actual_visits.account_id', $account->id)
                ->whereNotNull('actual_visits.ll')
                ->whereNotNull('actual_visits.lg')
                ->where('actual_visits.ll', '!=', '')
                ->where('actual_visits.lg', '!=', '')
                ->where('actual_visits.ll', '!=', '0')
                ->where('actual_visits.lg', '!=', '0')
                ->whereNull('actual_visits.deleted_at')
                ->join('users', 'actual_visits.user_id', '=', 'users.id');


            // Apply date filters if provided
            if ($fromDate) {
                $query->where('actual_visits.visit_date', '>=', Carbon::parse($fromDate));
            }

            if ($toDate) {
                $query->where('actual_visits.visit_date', '<=', Carbon::parse($toDate));
            }

            // Get the most recent visits with distinct coordinates and user information
            $visits = $query->select([
                'actual_visits.id',
                'actual_visits.visit_date',
                'actual_visits.ll',
                'actual_visits.lg',
                'actual_visits.visit_address',
                'actual_visits.account_id',
                'actual_visits.user_id',
                'users.fullname as user_name',
                'users.email as user_email'
            ])
                ->orderBy('actual_visits.visit_date', 'desc')
                ->limit($limit * 2) // Get more to filter for distinct locations
                ->get();

            // Filter for distinct locations (avoid duplicate coordinates)
            $distinctVisits = collect();
            $seenCoordinates = [];

            foreach ($visits as $visit) {
                $coordKey = round($visit->ll, 6) . ',' . round($visit->lg, 6);

                if (!in_array($coordKey, $seenCoordinates)) {
                    $distinctVisits->push($visit);
                    $seenCoordinates[] = $coordKey;

                    if ($distinctVisits->count() >= $limit) {
                        break;
                    }
                }
            }

            return $distinctVisits;
        } catch (\Exception $e) {
            Log::error('Error getting account visit locations', [
                'account_id' => $account->id,
                'error' => $e->getMessage()
            ]);

            return collect();
        }
    }

    /**
     * Validate input parameters for report generation
     *
     * @param array $options Input options to validate
     * @return array Validation result with errors if any
     */
    public function validateReportParameters(array $options): array
    {
        $errors = [];

        // Validate account_id if provided
        if (isset($options['account_id'])) {
            if (!is_numeric($options['account_id']) || $options['account_id'] <= 0) {
                $errors['account_id'] = 'Account ID must be a positive integer';
            } else {
                // Check if account exists
                $accountExists = Account::where('id', $options['account_id'])
                    ->whereNull('deleted_at')
                    ->exists();

                if (!$accountExists) {
                    $errors['account_id'] = 'Account not found or has been deleted';
                }
            }
        }

        // Validate line_ids if provided
        if (isset($options['line_ids'])) {
            if (!is_array($options['line_ids'])) {
                $errors['line_ids'] = 'Line IDs must be provided as an array';
            } else {
                $invalidLineIds = [];
                foreach ($options['line_ids'] as $lineId) {
                    if (!is_numeric($lineId) || $lineId <= 0) {
                        $invalidLineIds[] = $lineId;
                    } else {
                        // Check if line exists
                        $lineExists = Line::where('id', $lineId)
                            ->whereNull('deleted_at')
                            ->exists();

                        if (!$lineExists) {
                            $invalidLineIds[] = $lineId;
                        }
                    }
                }

                if (!empty($invalidLineIds)) {
                    $errors['line_ids'] = 'The following line IDs are invalid or do not exist: ' . implode(', ', $invalidLineIds);
                }
            }
        }

        // Validate div_ids if provided
        if (isset($options['div_ids'])) {
            if (!is_array($options['div_ids'])) {
                $errors['div_ids'] = 'Division IDs must be provided as an array';
            } else {
                $invalidDivIds = [];
                foreach ($options['div_ids'] as $divId) {
                    if (!is_numeric($divId) || $divId <= 0) {
                        $invalidDivIds[] = $divId;
                    } else {
                        // Check if division exists and is active
                        $divisionQuery = LineDivision::where('id', $divId)
                            ->whereNull('deleted_at')
                            ->where(function ($query) use ($options) {
                                $query->where(function ($subQuery) use ($options) {
                                    $subQuery->whereNull('to_date') // Active records
                                        ->orWhereBetween('to_date', [$options['from_date'], $options['to_date']]) // Ends within range
                                        ->orWhere('to_date', '>=', $options['to_date']); // Ends within range
                                })
                                    ->where(function ($subQuery) use ($options) {
                                        $subQuery->where('from_date', '<=', $options['from_date']) // Starts before range
                                            ->orWhereBetween('from_date', [$options['from_date'], $options['to_date']]); // Starts within range
                                    });
                            });
                        // ->where('from_date', '<=', Carbon::now())
                        // ->where(function ($query) {
                        //     $query->whereNull('to_date')
                        //         ->orWhere('to_date', '>=', Carbon::now());
                        // });


                        // If line_ids is also provided, ensure division belongs to one of those lines
                        if (isset($options['line_ids']) && !isset($errors['line_ids'])) {
                            $divisionQuery->whereIn('line_id', $options['line_ids']);
                        }

                        if (!$divisionQuery->exists()) {
                            $invalidDivIds[] = $divId;
                        }
                    }
                }

                if (!empty($invalidDivIds)) {
                    if (isset($options['line_ids'])) {
                        $errors['div_ids'] = 'The following division IDs are invalid, do not exist, are inactive, or do not belong to the specified lines: ' . implode(', ', $invalidDivIds);
                    } else {
                        $errors['div_ids'] = 'The following division IDs are invalid, do not exist, or are inactive: ' . implode(', ', $invalidDivIds);
                    }
                }
            }
        }

        // Validate account_type_ids if provided
        if (isset($options['account_type_ids'])) {
            if (!is_array($options['account_type_ids'])) {
                $errors['account_type_ids'] = 'Account Type IDs must be provided as an array';
            } else {
                $invalidAccountTypeIds = [];
                foreach ($options['account_type_ids'] as $accountTypeId) {
                    if (!is_numeric($accountTypeId) || $accountTypeId <= 0) {
                        $invalidAccountTypeIds[] = $accountTypeId;
                    } else {
                        // Check if account type exists
                        $accountTypeExists = AccountType::where('id', $accountTypeId)
                            ->whereNull('deleted_at')
                            ->exists();

                        if (!$accountTypeExists) {
                            $invalidAccountTypeIds[] = $accountTypeId;
                        }
                    }
                }

                if (!empty($invalidAccountTypeIds)) {
                    $errors['account_type_ids'] = 'The following account type IDs are invalid or do not exist: ' . implode(', ', $invalidAccountTypeIds);
                }
            }
        }

        // Validate date range
        if (isset($options['from_date'])) {
            try {
                Carbon::parse($options['from_date']);
            } catch (\Exception $e) {
                $errors['from_date'] = 'Invalid from_date format. Use YYYY-MM-DD';
            }
        }

        if (isset($options['to_date'])) {
            try {
                Carbon::parse($options['to_date']);
            } catch (\Exception $e) {
                $errors['to_date'] = 'Invalid to_date format. Use YYYY-MM-DD';
            }
        }

        // Validate date range logic
        if (isset($options['from_date']) && isset($options['to_date'])) {
            try {
                $fromDate = Carbon::parse($options['from_date']);
                $toDate = Carbon::parse($options['to_date']);

                if ($fromDate->gt($toDate)) {
                    $errors['date_range'] = 'from_date cannot be later than to_date';
                }
            } catch (\Exception $e) {
                // Date parsing errors already caught above
            }
        }

        // Validate distance threshold
        if (isset($options['distance_threshold'])) {
            if (!is_numeric($options['distance_threshold']) || $options['distance_threshold'] < 0) {
                $errors['distance_threshold'] = 'Distance threshold must be a non-negative number';
            }
        }

        // Validate recent visits limit
        if (isset($options['recent_visits_limit'])) {
            if (
                !is_numeric($options['recent_visits_limit']) ||
                $options['recent_visits_limit'] <= 0 ||
                $options['recent_visits_limit'] > 50
            ) {
                $errors['recent_visits_limit'] = 'Recent visits limit must be between 1 and 50';
            }
        }

        // Validate location filter
        if (isset($options['location_filter'])) {
            if (!is_string($options['location_filter'])) {
                $errors['location_filter'] = 'Location filter must be a string';
            } elseif (!in_array($options['location_filter'], ['with_location', 'without_location'])) {
                $errors['location_filter'] = 'Location filter must be either "with_location" or "without_location"';
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Get all visit coordinates for an account (for frequency calculation) with LineDivision context
     *
     * @param Account $account Account to get all visits for
     * @return Collection Collection of visit coordinates with user information
     */
    private function getAllAccountVisitCoordinates(Account $account): Collection
    {
        try {
            $query = ActualVisit::where('account_id', $account->id)
                ->whereNotNull('ll')
                ->whereNotNull('lg')
                ->where('ll', '!=', '')
                ->where('lg', '!=', '')
                ->where('ll', '!=', '0')
                ->where('lg', '!=', '0')
                ->whereNull('deleted_at');

            return $query->select(['ll', 'lg', 'visit_date', 'user_id'])->get();
        } catch (\Exception $e) {
            Log::error('Error getting all account visit coordinates', [
                'account_id' => $account->id,
                'error' => $e->getMessage()
            ]);

            return collect();
        }
    }

    /**
     * Calculate visit frequency within radius for a specific coordinate
     * Counts unique users who have visited within the radius, not total visits
     *
     * @param string $latitude Target latitude
     * @param string $longitude Target longitude
     * @param Collection $allVisitCoordinates All visit coordinates for the account with user information
     * @return int Number of unique users who have visited within the frequency radius
     */
    private function calculateVisitFrequency(
        string $latitude,
        string $longitude,
        Collection $allVisitCoordinates
    ): int {
        try {
            $uniqueUsers = [];
            $targetLat = (float) $latitude;
            $targetLng = (float) $longitude;

            foreach ($allVisitCoordinates as $visitCoordinate) {
                $visitLat = (float) $visitCoordinate->ll;
                $visitLng = (float) $visitCoordinate->lg;

                // Calculate distance between target coordinate and this visit coordinate
                $distance = $this->distanceCalculator->distanceBetweenTwoCoordinates(
                    $targetLat,
                    $targetLng,
                    $visitLat,
                    $visitLng,
                    'M' // meters
                );

                // Count unique users within the frequency radius
                if ($distance <= self::FREQUENCY_RADIUS_METERS) {
                    $userId = $visitCoordinate->user_id ?? 'unknown';
                    if (!in_array($userId, $uniqueUsers)) {
                        $uniqueUsers[] = $userId;
                    }
                }
            }

            return count($uniqueUsers);
        } catch (\Exception $e) {
            Log::error('Error calculating visit frequency', [
                'latitude' => $latitude,
                'longitude' => $longitude,
                'error' => $e->getMessage()
            ]);

            // Default to 1 if calculation fails
            return 1;
        }
    }

    /**
     * Format user data for visit location response
     *
     * @param object $visit Visit object with user information
     * @return array Formatted user data
     */
    private function formatUserData($visit): array
    {
        try {
            // Check if user information is available
            if (!empty($visit->user_id)) {
                return [
                    'id' => $visit->user_id,
                    'name' => $visit->user_name ?? 'Unknown User',
                    'email' => $visit->user_email ?? null
                ];
            }

            // Return fallback data if user information is missing
            return [
                'id' => null,
                'name' => 'Unknown User',
                'email' => null
            ];
        } catch (\Exception $e) {
            Log::error('Error formatting user data for visit', [
                'visit_id' => $visit->id ?? 'unknown',
                'user_id' => $visit->user_id ?? 'unknown',
                'error' => $e->getMessage()
            ]);

            // Return fallback data on error
            return [
                'id' => null,
                'name' => 'Unknown User',
                'email' => null
            ];
        }
    }
}

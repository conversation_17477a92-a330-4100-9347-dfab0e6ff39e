<?php

namespace App\Http\Controllers;

use App\Action;
use App\ActualVisit;
use App\Exceptions\CrmException;
use App\Form;
use App\Helpers\LogActivity;
use App\Http\Controllers\Controller;
use App\Line;
use App\LineDivision;
use App\LineDivisionType;
use App\Models\QuizCategory;
use App\Models\QuizResult;
use App\Permission;
use App\Product;
use App\User;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class QuizResultSuumaryReportController extends ApiController
{
    public function getLineData(Request $request)
    {
        /**@var User $user */
        $user = Auth::user();
        $lines = Line::whereIntegerInRaw('id', $request->lines)->get();
        $users = $user->belowUsersOfAllLinesWithPositions($lines);
        return response()->json([
            'role' => $user,
            'users' => $users,
        ]);
    }

    public function filter(Request $request)
    {
        /**@var User authUser */
        $authUser = Auth::user();
        $quiz = $request->quizResultFilter;
        // $lines = $request->quizResultFilter['line'];
        $from = $request->quizResultFilter['from'];
        $to = $request->quizResultFilter['to'];
        $data = new Collection([]);
        $filtered = new Collection([]);
        $lines = Line::when(!empty($quiz['line']), fn($q) => $q->whereIn("lines.id", $quiz['line']))->get();;
        $categories = QuizCategory::select('quiz_categories.name', 'quiz_categories.id')->get();
        $fields = collect(['line', 'employee', 'No_of_Quizzes']);
        $clickable_fields = collect(['No_of_Quizzes',]);

        $categories->each(function ($object) use (&$fields, &$clickable_fields) {
            $fields = $fields->merge($object->name . ' Score');
            $fields = $fields->merge($object->name . ' Questions');
            $clickable_fields = $clickable_fields->merge($object->name . ' Score');
            // $clickable_fields = $clickable_fields->merge($object->name . ' Questions');
        });
        $fields = $fields->merge('Avg_Score');
        foreach ($lines as $line) {
            $users = $line->users($from, $to)->select('users.id', 'users.fullname')
                ->when(!empty($quiz['users']), fn($q) => $q->whereIn("users.id", $quiz['users']))->get();
            $filtered = $filtered->merge($authUser->filterUsers($line, $users, $quiz));
        }

        // throw new CrmException($data);
        $filtered->unique('id')->values()->each(function ($object) use (&$data, $categories, $from, $to) {
            $data = $data->push($this->statistics($object, $categories, $from, $to));
        });
        return response()->json([
            'data' => $data->unique("id")->values(),
            'fields' => $fields,
            'clickable_fields' => $clickable_fields
        ]);
    }

    private function statistics($object, $categories, $from, $to)
    {

        $data = collect([
            'id' => $object->id,
            'line' => count($object->lines($from, $to)->pluck('name')) > 3 ? $object->lines($from, $to)->first()?->name : $object->lines($from, $to)->pluck('name')->implode(','),
            'employee' => $object->fullname,
            'color' => $object->divisions($from, $to)?->first()?->DivisionType->color,
        ]);

        $category_results = collect([]);

        $quiz_no = DB::table('quiz_result')
            ->select(
                'quiz_result.quiz_id',
                'users.name',
            )
            ->leftJoin('quizzes', 'quiz_result.quiz_id', 'quizzes.id')
            ->leftJoin('users', 'quiz_result.user_id', 'users.id')
            ->where('quiz_result.user_id', '=', $object->id)
            ->whereBetween('quiz_result.created_at', [Carbon::parse($from)->startOfDay(), Carbon::parse($to)->endOfDay()])
            ->get()->unique('quiz_id')->count();

        $data->put('No_of_Quizzes', $quiz_no);
        $Quizzes_ids = [];
        $avg_result_of_categories = 0;
        foreach ($categories as $category) {
            $categeory_result_sum = 0;
            $category_question_numbers = 0;
            $category_results =  DB::table('quiz_result')
                ->select(
                    'quiz_result.id',
                    'quiz_result.question_id',
                    'quiz_result.result',
                    'quiz_questions.quiz_question_level_id',
                    'quiz_questions.quiz_category_id',
                    'quiz_categories.name as category',
                    'quiz_categories.id as category_id',
                    'quizzes.id as quiz_id',
                    'quizzes.quiz_degree',
                    'users.fullname',
                    'quiz_details.degree',
                    'quiz_details.question_number'
                )
                ->join('quiz_questions', 'quiz_result.question_id', '=', 'quiz_questions.id')
                ->join('quizzes', 'quiz_result.quiz_id', '=', 'quizzes.id')
                ->join('users', 'quiz_result.user_id', '=', 'users.id')
                ->join('quiz_categories', 'quiz_questions.quiz_category_id', '=', 'quiz_categories.id')
                // Join quiz_details using quiz_id + level (not question_id!)
                ->leftJoin('quiz_details', function ($join) {
                    $join->on('quiz_result.quiz_id', '=', 'quiz_details.quiz_id')
                        ->on('quiz_questions.quiz_question_level_id', '=', 'quiz_details.quiz_question_level_id');
                })
                ->where('quiz_result.user_id', $object->id)
                ->where('quiz_questions.quiz_category_id', $category->id)
                ->where('quiz_details.quiz_category_id', $category->id)
                ->whereBetween('quiz_result.created_at', [
                    Carbon::parse($from)->startOfDay(),
                    Carbon::parse($to)->endOfDay()
                ])
                ->groupBy(
                    'quiz_result.id',
                    'quiz_result.question_id',
                    'quiz_result.result',
                    'quiz_questions.quiz_question_level_id',
                    'quiz_questions.quiz_category_id',
                    'quiz_categories.name',
                    'quizzes.id',
                    'quizzes.quiz_degree',
                    'users.fullname',
                    'quiz_details.degree',
                    'quiz_details.question_number'
                )
                ->get();
            $category_results_count = $category_results->count();
            $categeory_result_sum += $category_results->sum('result');
            $category_question_numbers = $category_results->count('question_id');
            $data->put($category->name . ' Questions', $category_question_numbers ?? 0);
            $data->put($category->name . ' Score', $categeory_result_sum ?? 0);

            foreach ($category_results as $category_result) {
                $avg_result_of_categories += ((float)$category_result->result / ((float)$category_result->degree * $category_results_count));
                $data->put($category->name . ' ID', $category_result->category_id);
                array_push($Quizzes_ids, $category_result->quiz_id);
            }
        }
        $data->put('Avg_Score', round($avg_result_of_categories, 2));

        $Quizzes_ids = array_values(array_unique($Quizzes_ids));

        $data->put('Quizzes_ids', $Quizzes_ids);

        return $data;
    }

    public function showDataNoOfQuizzes(Request $request)
    {
        $user = User::find($request->user_id);
        $from = Carbon::parse($request->from)->startOfDay();
        $to = Carbon::parse($request->to)->endOfDay();
        $quizzes_ids = $request->Quizzes_ids;

        $quizzes = DB::table('quiz_result')
            ->select(
                'quiz_result.quiz_id',
                'quizzes.name as quiz_name',
            )
            ->leftJoin('quiz_answers', 'quiz_result.answer_id', 'quiz_answers.id')
            ->leftJoin('quiz_questions', 'quiz_result.question_id', 'quiz_questions.id')
            ->leftJoin('quizzes', 'quiz_result.quiz_id', 'quizzes.id')
            ->leftJoin('users', 'quiz_result.user_id', 'users.id')
            ->leftJoin('quiz_details', 'quiz_result.quiz_id', 'quiz_details.quiz_id')
            ->leftJoin('quiz_categories', 'quiz_questions.quiz_category_id', 'quiz_categories.id')
            ->where('quiz_result.user_id', '=', $user->id)
            ->whereIntegerInRaw('quiz_result.quiz_id', $quizzes_ids)
            ->whereBetween('quiz_result.created_at', [Carbon::parse($from)->startOfDay(), Carbon::parse($to)->endOfDay()])
            ->groupBy('quiz_result.quiz_id', 'quizzes.name')
            ->get();

        $data = $quizzes->map(function ($quiz) use ($user) {
            return [
                'quiz_id' => $quiz->quiz_id,
                'quiz_name' => $quiz->quiz_name,
                'employee' => $user->fullname,
                'emp_id' => $user->id,
                'result' => QuizResult::where('quiz_id', $quiz->quiz_id)->where('user_id', $user->id)->sum('result'),
                'actions' => '',
            ];
        });

        return $this->respond($data);
    }

    public function showDataCategoryQuestions(Request $request)
    {
        $user = User::find($request->user_id);
        $from = Carbon::parse($request->from)->startOfDay();
        $to = Carbon::parse($request->to)->endOfDay();
        $quizzes_ids = $request->Quizzes_ids;
        $category_name = $request->categoryName;

        $category = DB::table('quiz_categories')
            ->select('quiz_categories.id', 'quiz_categories.name')
            ->where('quiz_categories.name', '=', $category_name)
            ->first();
        $category_id = $category->id;
        // foreach ($quizzes_ids as $quiz) {
        $quiz_results = DB::table('quiz_result')
            ->select(
                'quiz_result.id',
                'quiz_result.quiz_id',
                'quizzes.name as quiz_name',
                'quiz_categories.name as category_name',
                'quiz_result.result',
                'quiz_questions.name as question',
                'quiz_answers.name as answer',
            )
            ->leftJoin('quiz_answers', 'quiz_result.answer_id', 'quiz_answers.id')
            ->leftJoin('quiz_questions', 'quiz_result.question_id', 'quiz_questions.id')
            ->leftJoin('quizzes', 'quiz_result.quiz_id', 'quizzes.id')
            ->leftJoin('users', 'quiz_result.user_id', 'users.id')
            ->leftJoin('quiz_details', 'quiz_result.quiz_id', 'quiz_details.quiz_id')
            ->leftJoin('quiz_categories', 'quiz_questions.quiz_category_id', 'quiz_categories.id')
            ->where('quiz_result.user_id', '=', $user->id)
            ->where('quiz_categories.id', '=', $category_id)
            ->whereIntegerInRaw('quiz_result.quiz_id', $quizzes_ids)
            ->whereBetween('quiz_result.created_at', [Carbon::parse($from)->startOfDay(), Carbon::parse($to)->endOfDay()])
            ->groupBy('quiz_result.id', 'quiz_questions.name', 'quiz_answers.name', 'quiz_categories.name', 'quizzes.name')
            ->get();

        $data = $quiz_results->map(function ($questions) {
            return [
                'quiz_id' => $questions->quiz_id,
                'quiz_name' => $questions->quiz_name,
                'category_name' => $questions->category_name,
                'question' => $questions->question,
                'answer' => $questions->answer,
                'result' => $questions->result,
            ];
        });

        return $this->respond($data);
        // }
    }
}

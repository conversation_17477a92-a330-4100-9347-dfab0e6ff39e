<?php

namespace App\Http\Controllers\Sales;

use App\Http\Controllers\ApiController;
use App\Services\Sales\SalesIncentiveCalculationService;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;

class SalesIncentiveCalculationReportController extends ApiController
{

    public function __construct(private readonly SalesIncentiveCalculationService $salesIncentiveCalculationService)
    {
    }


    public function getLines(Request $request)
    {
        $from = Carbon::parse($request->from)->startOfDay();
        $to = Carbon::parse($request->to)->endOfDay();
        return $this->respond($this->salesIncentiveCalculationService->getLines($from, $to));
    }

    public function getLineData(Request $request)
    {
        $from = Carbon::parse($request->from)->startOfDay();
        $to = Carbon::parse($request->to)->endOfDay();
        // throw new CrmException([$from,$to]);
        return $this->respond($this->salesIncentiveCalculationService->getLineData($request->lines, $from, $to));
    }


    public function getProductData(Request $request)
    {
        $from = Carbon::parse($request->from)->startOfDay();
        $to = Carbon::parse($request->to)->endOfDay();
        return $this->respond($this->salesIncentiveCalculationService->getProductData($request->lines, $request->type_id, $from, $to));
    }


    public function filter(Request $request)
    {
        try {
            $data = $this->salesIncentiveCalculationService->filter($request->saleFilter);

            return response()->json($data);
        } catch (\Exception $e) {
            Log::channel('reports')->error('SalesIncentiveReport: ' . $e->getMessage());
            Log::channel('reports')->error('SalesIncentiveReportLine: ' . $e->getLine());
            throw $e;
        }

    }


}

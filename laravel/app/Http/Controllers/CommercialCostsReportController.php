<?php

namespace App\Http\Controllers;

use App\AccountLines;
use App\ActualVisitSetting;
use App\Brand;
use App\Exceptions\CrmException;
use App\Line;
use App\LinkedParmaciesSetting;
use App\Mapping;
use App\MappingUnifiedCode;
use App\Models\CommercialRequest\CommercialDoctor;
use App\Models\CommercialRequest\CommercialOutOfList;
use App\Models\CommercialRequest\CommercialRequest;
use App\Models\LinkedPharmacy;
use App\Models\NewAccountDoctor;
use App\Product;
use App\ProductBrands;
use App\RequestType;
use App\Sale;
use App\SaleDetail;
use App\Setting;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CommercialCostsReportController extends ApiController
{
    public function getCommercialProducts($commercial, $from, $to, $types)
    {
        $commercials = DB::table('commercial_requests')->select(
            'commercial_requests.id as id',
            'commercial_requests.description',
            DB::raw('IFNULL(DATE_FORMAT(crm_commercial_requests.from_date,"%d-%m-%Y"),"") as activity_date'),
            DB::raw('IFNULL(DATE_FORMAT(crm_commercial_requests.created_at,"%d-%m-%Y"),"") as insertion_date'),
            'request_types.name as type',
            // 'users.name as employee',
            // 'users.id as user_id',
            DB::raw('IFNULL(group_concat(distinct crm_lines.name),"") as line'),
            DB::raw('IFNULL(group_concat(distinct crm_commercial_doctors.doctor_id),"") as doctorIds'),
            "line_divisions.name as division",
            "line_divisions.id as div_id",
            "products.name as product",
            "products.id as product_ids",
            'commercial_products.ratio as ratio',
            DB::raw('IFNULL(group_concat(distinct crm_payment_methods.name),"") as payment'),
            DB::raw('IFNULL(group_concat(distinct crm_division_types.color),"") as color'),
            DB::raw('IFNULL(COUNT(distinct crm_commercial_out_of_lists.id),0) as out_doctors'),
            DB::raw('IFNULL(crm_commercial_requests.amount,"") as total'),
            DB::raw('IFNULL(crm_plan_visit_details.approval,"pending") as status'),
        )
            ->selectRaw('COUNT(distinct crm_commercial_doctors.id) + COUNT(distinct crm_commercial_out_of_lists.id)  as total_docs')
            ->selectRaw('COUNT(distinct crm_commercial_doctors.id) as request_docs')
            ->leftJoin('request_types', 'commercial_requests.request_type_id', 'request_types.id')
            ->leftJoin('commercial_lines', 'commercial_requests.id', 'commercial_lines.request_id')
            ->leftJoin('commercial_categories_costs', 'commercial_requests.id', 'commercial_categories_costs.request_id')
            ->leftJoin('payment_methods', 'commercial_categories_costs.payment_method_id', 'payment_methods.id')
            ->leftJoin(
                'plan_visit_details',
                function ($join) {
                    $join->on('commercial_requests.id', '=', 'plan_visit_details.visitable_id');
                    $join->where('plan_visit_details.visitable_type', 'App\Models\CommercialRequest\CommercialRequest');
                }
            )
            ->leftJoin('lines', 'commercial_lines.line_id', 'lines.id')
            ->leftJoin('commercial_divisions', 'commercial_requests.id', 'commercial_divisions.request_id')
            ->leftJoin('line_divisions', 'commercial_divisions.div_id', 'line_divisions.id')
            ->leftJoin('division_types', 'line_divisions.division_type_id', 'division_types.id')
            ->leftJoin('commercial_products', 'commercial_requests.id', 'commercial_products.request_id')
            ->leftJoin('products', 'commercial_products.product_id', 'products.id')
            ->leftJoin('commercial_out_of_lists', 'commercial_requests.id', 'commercial_out_of_lists.request_id')
            ->leftJoin('commercial_doctors', 'commercial_requests.id', 'commercial_doctors.request_id')
            ->leftJoin('doctors', 'commercial_doctors.doctor_id', 'doctors.id')
            ->orderBy('commercial_requests.id', 'DESC')
            ->whereNull('commercial_requests.deleted_at')
            ->whereIntegerInRaw('commercial_requests.request_type_id', $types->pluck('id')->toArray())
            ->whereBetween('commercial_requests.created_at', [$from, $to]);
        $commercials = match ($commercial['approval']) {
            1 => $commercials->whereNull('plan_visit_details.approval'),
            2 => $commercials->where('plan_visit_details.approval', 1),
            3 => $commercials->where('plan_visit_details.approval', 0),
            4 => $commercials->where(fn($q) => $q->where('plan_visit_details.approval', 1)->orWhere('plan_visit_details.approval', null)),
            5 => $commercials,
        };

        if (!empty($commercial['products'])) {
            $commercials = $commercials->whereIntegerInRaw('commercial_products.product_id', $commercial['products']);
        }
        if (!empty($commercial['lines'])) {
            $commercials = $commercials->whereIntegerInRaw('lines.id', $commercial['lines']);
        }
        if (!empty($commercial['payments'])) {
            $commercials = $commercials->whereIntegerInRaw('commercial_categories_costs.payment_method_id', $commercial['payments']);
        }

        $commercials = $commercials->groupBy("commercial_requests.id", "commercial_requests.description", "line_divisions.id", "products.id", 'commercial_products.ratio', 'plan_visit_details.approval')->get();

        return $commercials;
    }
    public function getCommercials($commercial, $from, $to, $types)
    {
        $commercials = DB::table('commercial_requests')->select(
            'commercial_requests.id as id',
            'commercial_requests.description',
            DB::raw('IFNULL(DATE_FORMAT(crm_commercial_requests.from_date,"%d-%m-%Y"),"") as activity_date'),
            DB::raw('IFNULL(DATE_FORMAT(crm_commercial_requests.created_at,"%d-%m-%Y"),"") as insertion_date'),
            'request_types.name as type',
            DB::raw('IFNULL(group_concat(distinct crm_lines.name),"") as line'),
            DB::raw('IFNULL(group_concat(distinct crm_commercial_doctors.doctor_id),"") as doctorIds'),
            "line_divisions.name as division",
            "line_divisions.id as div_id",
            "brands.name as brand",
            "brands.id as brand_id",
            'commercial_products.ratio as ratio',
            DB::raw('IFNULL(group_concat(distinct crm_payment_methods.name),"") as payment'),
            DB::raw('IFNULL(group_concat(distinct crm_division_types.color),"") as color'),
            DB::raw('IFNULL(group_concat(distinct crm_products_of_brand.id),"") as product_ids'),
            DB::raw('IFNULL(group_concat(distinct crm_products_of_brand.name),"") as product'),
            DB::raw('IFNULL(COUNT(distinct crm_commercial_out_of_lists.id),0) as out_doctors'),
            DB::raw('IFNULL(crm_commercial_requests.amount,"") as total'),
            DB::raw('IFNULL(crm_plan_visit_details.approval,"pending") as status'),
        )
            ->selectRaw('COUNT(distinct crm_commercial_doctors.id) + COUNT(distinct crm_commercial_out_of_lists.id)  as total_docs')
            ->selectRaw('COUNT(distinct crm_commercial_doctors.id) as request_docs')
            ->join('request_types', function ($join) use ($types) {
                $join->on('commercial_requests.request_type_id', 'request_types.id')
                    ->whereNull('request_types.deleted_at')
                    ->whereIntegerInRaw('request_types.id', $types->pluck('id')->toArray());
            })
            // ->leftJoin('request_types', 'commercial_requests.request_type_id', 'request_types.id')
            ->join('commercial_lines', function ($join) use ($commercial, $from, $to) {
                $join->on('commercial_requests.id', 'commercial_lines.request_id')
                    ->whereNull('commercial_lines.deleted_at')
                    ->whereBetween('commercial_lines.created_at', [$from, $to]);
                if (!empty($commercial['lines'])) {
                    $join->whereIntegerInRaw('commercial_lines.line_id', $commercial['lines']);
                }
            })
            ->leftJoin('commercial_categories_costs', function ($join) use ($commercial) {
                $join->on('commercial_requests.id', 'commercial_categories_costs.request_id')
                    ->whereNull('commercial_categories_costs.deleted_at');
                if (!empty($commercial['payments'])) {
                    $join->whereIntegerInRaw('commercial_categories_costs.payment_method_id', $commercial['payments']);
                }
            })
            ->leftJoin('payment_methods', 'commercial_categories_costs.payment_method_id', 'payment_methods.id')
            ->leftJoin(
                'plan_visit_details',
                function ($join) use ($commercial) {
                    $join->on('commercial_requests.id', '=', 'plan_visit_details.visitable_id');
                    $join->where('plan_visit_details.visitable_type', 'App\Models\CommercialRequest\CommercialRequest');
                    $commercials = match ($commercial['approval']) {
                        1 => $join->whereNull('plan_visit_details.approval'),
                        2 => $join->where('plan_visit_details.approval', 1),
                        3 => $join->where('plan_visit_details.approval', 0),
                        4 => $join->where(fn($q) => $q->where('plan_visit_details.approval', 1)->orWhere('plan_visit_details.approval', null)),
                        5 => $join,
                    };
                }
            )
            ->join('lines', function ($join) use ($commercial) {
                $join->on('commercial_lines.line_id', 'lines.id')
                    ->whereNull('lines.deleted_at');
                if (!empty($commercial['lines'])) {
                    $join->whereIntegerInRaw('lines.id', $commercial['lines']);
                }
            })
            ->leftJoin('commercial_divisions', 'commercial_requests.id', 'commercial_divisions.request_id')
            ->leftJoin('line_divisions', 'commercial_divisions.div_id', 'line_divisions.id')
            ->leftJoin('division_types', 'line_divisions.division_type_id', 'division_types.id')
            ->join('commercial_products', function ($join) use ($commercial) {
                $join->on('commercial_requests.id', 'commercial_products.request_id')
                    ->whereNull('commercial_products.deleted_at');
                if (!empty($commercial['products'])) {
                    $join->whereIntegerInRaw('commercial_products.product_id', $commercial['products']);
                }
            })
            ->join('products as products_of_commercial', 'commercial_products.product_id', 'products_of_commercial.id')
            ->leftJoin('product_brands as products_brands_of_commercial', 'products_of_commercial.id', 'products_brands_of_commercial.product_id')
            ->leftJoin('brands', 'products_brands_of_commercial.brand_id', 'brands.id')
            ->leftJoin('product_brands as brands_products', 'brands.id', 'brands_products.brand_id')
            ->leftJoin('products as products_of_brand', 'brands_products.product_id', 'products_of_brand.id')
            ->leftJoin('commercial_out_of_lists', 'commercial_requests.id', 'commercial_out_of_lists.request_id')
            ->leftJoin('commercial_doctors', 'commercial_requests.id', 'commercial_doctors.request_id')
            ->leftJoin('doctors', 'commercial_doctors.doctor_id', 'doctors.id')
            ->orderBy('commercial_requests.id', 'DESC')
            ->whereNull('commercial_requests.deleted_at')
            ->whereBetween('commercial_requests.created_at', [$from, $to]);

        $commercials = $commercials->groupBy(
            "commercial_requests.id",
            "commercial_requests.description",
            "line_divisions.id",
            "brands.id",
            'commercial_products.ratio',
            'plan_visit_details.approval'
        )->get();

        return $commercials;
    }


    public function getLineData(Request $request)
    {
        $products = Product::whereHas(
            'lineproducts',
            fn($q) =>
            $q->whereIntegerInRaw('line_products.line_id', $request->lines)->where('line_products.from_date', '<=', now())
                ->where(fn($q) => $q->where('line_products.to_date', '>', (string)Carbon::now())
                    ->orWhere('line_products.to_date', null))
        )->get();
        return $this->respond($products);
    }
    public function filter(Request $request)
    {
        $perBrand = Setting::where('key', 'reports_level')->value('value') == 'Brand';
        $settingLinked = LinkedParmaciesSetting::where('key', 'type')->value('linked_from');

        /**@var User authUser */
        $authUser = Auth::user();
        $commercial = $request->commercialFilter;
        $from = Carbon::parse($commercial['fromDate'])->startOfDay();
        $to = Carbon::parse($commercial['toDate'])->endOfDay();
        $types = RequestType::select('request_types.id', 'request_types.name')
            ->when(!empty($commercial['types']), fn($q) => $q->whereIn("request_types.id", $commercial['types']))->get();
        $fields = collect([
            'id',
            'title',
            'total_docs',
            'line',
            'type',
            'insertion_date',
            'activity_date',
            'payment',
            'status',
            'out_list',
            'no_of_docs',
            'division',
            'product',
            'pro_cost',
            'dr_cost',
            'avg_sales_before',
            'avg_sales_after',
            'brick',
            'brick_sales_before',
            'brick_sales_after'
        ]);
        $clickable_fields = collect(['total_docs']);
        $data = $this->statistics($types, $commercial, $from, $to, $perBrand, $settingLinked);
        return response()->json([
            'data' => $data,
            'fields' => $fields,
            'clickable_fields' => $clickable_fields
        ]);
    }
    private function statistics($types, $commercial, $from, $to, $perBrand, $settingLinked)
    {
        // $div = null;
        // $request_id = null;
        $no_of_docs = 0;
        $brick = null;
        $brick_id = null;
        $accountIds = [];
        $allMonths = [];
        // $pharmacies = collect([]);

        // $number_of_month_after = $settings->where("key", "number_of_month_after")->first()->value;
        // $number_of_month_before = $settings->where("key", "number_of_month_befor")->first()->value;

        $monthsBefore = LinkedParmaciesSetting::where('key', 'number_of_month_befor')->value('value') ?? 2;
        $monthsAfter = LinkedParmaciesSetting::where('key', 'number_of_month_after')->value('value') ?? 3;
        $commercials = collect([]);
        if ($perBrand) {
            $commercials = $this->getCommercials($commercial, $from, $to, $types);
        } else {
            $commercials = $this->getCommercialProducts($commercial, $from, $to, $types);
        }

        $allDoctors = $this->getDoctorsPerDivisions($commercials->pluck('id')->toArray(), $from, $to);
        $data = [];
        foreach ($commercials as $object) {
            $monthsBeforeArray =  [
                Carbon::parse($object->insertion_date)->subMonth($monthsBefore)->startOfMonth()->toDateString(),
                Carbon::parse($object->insertion_date)->subMonth()->endOfMonth()->toDateString()
            ];
            $monthsAfterArray =  [
                Carbon::parse($object->insertion_date)->addMonth()->startOfMonth()->toDateString(),
                Carbon::parse($object->insertion_date)->addMonth($monthsAfter)->endOfMonth()->toDateString()
            ];
            // $allMonths = $this->doctorMonth($object, $number_of_month_before, $number_of_month_after);
            $perRequest = $allDoctors[$object->id] ?? null;
            $countDivisions = ($perRequest == null) ? 1 : count($perRequest);
            if ($object->total_docs == 0) {
                continue;
                // $countDivisions = DB::table('commercial_divisions')->select('div_id')->where('request_id', $object->id)->count();
            }

            $perDivision = $perRequest[$object->div_id] ?? null;
            // if ($perDivision == null) {
            //     continue;
            // }
            $doctors = $perDivision?->first();

            $no_of_docs = $doctors?->count;
            $brick = $doctors?->brick;
            $brick_id = $doctors?->brick_id;
            $accountIds = [];
            if ($doctors?->account_ids !== null) {
                $accountIds = explode(",", $doctors?->account_ids);
            }
            $accountIds = explode(",", $doctors?->account_ids);
            $pharmacies = $this->getPharmacies($accountIds, $settingLinked);
            $invitedDocs =  $object->total_docs - intval($object->out_doctors);

            $condition = $invitedDocs > 0 && $no_of_docs > 0;
            $proRatio = ($object->ratio * $object->total) / 100;

            $pro_cost =  $countDivisions > 0 ?
                round($proRatio / $countDivisions, 1) : 0;
            $pro_weight =  $condition ? round($proRatio / $invitedDocs, 2) : 0;
            $products = explode(",", $object?->product_ids);
            $data[] = [
                'id' => $object->id,
                'title' => $object->description,
                'total_docs' => $object->total_docs,
                'line' => $object->line,
                'type' => $object->type,
                'insertion_date' => $object->insertion_date,
                'activity_date' => $object->activity_date,
                'status' => $object->status == 1 ? 'Approved' : ($object->status == 0 ? 'DisApproved' : 'Pending'),
                'payment' => $object->payment,
                'out_list' => $object->out_doctors,
                'no_of_docs' => $no_of_docs ?? 0,
                'division' => $object->division ?? '',
                'product' => $perBrand ? $object->brand
                    : $object->product,
                'pro_cost' => $pro_cost ?? 0,
                'dr_cost' => $pro_weight ?? 0,
                'avg_sales_before' => $this->getAvgSales($object->div_id, $products, $pharmacies, $monthsBeforeArray, $monthsBefore),
                'avg_sales_after' => $this->getAvgSales($object->div_id, $products, $pharmacies, $monthsAfterArray, $monthsAfter),
                'brick' => $brick ?? '',
                'brick_sales_before' => $this->getSales($object->div_id, $products, $brick_id, $monthsBeforeArray),
                'brick_sales_after' => $this->getSales($object->div_id, $products, $brick_id, $monthsAfterArray),
                'color' => $object->color,
            ];
        }

        $commercialWithoutDoctors = $commercials->where('total_docs', '=', 0)->unique(function ($item) {
            return $item->id . $item->brand_id;
        })->values();


        if (count($commercialWithoutDoctors) > 0) {
            foreach ($commercialWithoutDoctors as $commercialWithoutDoctor) {
                $proRatio = round(($commercialWithoutDoctor->ratio * $commercialWithoutDoctor->total) / 100, 2);
                $data[] = [
                    'id' => $commercialWithoutDoctor->id,
                    'title' => $commercialWithoutDoctor->description,
                    'total_docs' => 0,
                    'line' => $commercialWithoutDoctor->line,
                    'type' => $commercialWithoutDoctor->type,
                    'insertion_date' => $commercialWithoutDoctor->insertion_date,
                    'activity_date' => $commercialWithoutDoctor->activity_date,
                    'status' => $commercialWithoutDoctor->status,
                    'payment' => $commercialWithoutDoctor->payment,
                    'out_list' => 0,
                    'no_of_docs' => 0,
                    'division' => '',
                    'product' => $commercialWithoutDoctor->brand,
                    'pro_cost' => $proRatio ?? 0,
                    'dr_cost' => 0,
                    'avg_sales_before' => 0,
                    'avg_sales_after' => 0,
                    'brick' => '',
                    'brick_sales_before' => 0,
                    'brick_sales_after' => 0,
                    'color' => '',
                ];
            }
        }
        return $data;
    }

    private function getDoctorsPerDivisions(array $request_ids, Carbon $from, Carbon $to)
    {
        return CommercialDoctor::select(
            'commercial_doctors.request_id',
            'commercial_divisions.div_id as div_id',
            DB::raw('IFNULL(group_concat(distinct crm_account_lines.account_id),"") as account_ids'),
            // 'bricks.name as brick',
            // 'bricks.id as brick_id',
            DB::raw('IFNULL(group_concat(distinct crm_bricks.name),"") as brick'),
            DB::raw('IFNULL(group_concat(distinct crm_bricks.id),"") as brick_id'),
            DB::raw('COUNT(distinct crm_new_account_doctors.doctor_id) as count'),
        )
            ->leftJoin('commercial_divisions', 'commercial_doctors.request_id', 'commercial_divisions.request_id')
            ->leftjoin('account_lines', function ($join) use ($from, $to) {
                $join->on('commercial_doctors.account_id', 'account_lines.account_id')
                    ->on('commercial_divisions.div_id', 'account_lines.line_division_id')
                    // ->whereIn("account_lines.line_division_id", $divs)
                    ->where('account_lines.from_date', '<=', $from->toDateString())
                    ->where(fn($q) => $q->where('account_lines.to_date', '>=', $to->toDateString())
                        ->orWhere('account_lines.to_date', null));
            })
            ->leftJoin('new_account_doctors', function ($join) use ($from, $to) {
                $join->on('account_lines.id', 'new_account_doctors.account_lines_id')
                    ->on('commercial_doctors.doctor_id', 'new_account_doctors.doctor_id')
                    ->where('new_account_doctors.from_date', '<=', $from->toDateString())
                    ->where(fn($q) => $q->where('new_account_doctors.to_date', '>=', $to->toDateString())
                        ->orWhere('new_account_doctors.to_date', null));
            })
            ->leftJoin('bricks', 'account_lines.brick_id', 'bricks.id')
            ->whereIntegerInRaw("commercial_doctors.request_id", $request_ids)
            ->groupBy('commercial_doctors.request_id', "commercial_divisions.div_id")
            ->get()
            ->groupBy(["request_id", "div_id"]);
    }
    private function getPharmacies($accounts, $setting)
    {
        $type = match ($setting) {
            'mapping' => Mapping::class,
            'unified' =>  MappingUnifiedCode::class
        };
        return LinkedPharmacy::select(DB::raw("distinct pharmable_id"))->whereIntegerInRaw('account_id', $accounts)
            ->where('pharmable_type', $type)
            ->get()->pluck('pharmable_id');
    }
    // public function doctorMonth($commercial, $number_of_month_befor, $number_of_month_after)
    // {
    //     $before = [];
    //     $after = [];
    //     for ($i = $number_of_month_befor; $i >= 1; $i--) {
    //         array_push($before, Carbon::parse($commercial->insertion_date)->startOfMonth()->subMonths($i)->endOfMonth()->format('Y-m'));
    //     }
    //     for ($i = 1; $i <= $number_of_month_after; $i++) {
    //         array_push($after, Carbon::parse($commercial->insertion_date)->addMonths($i)->format('Y-m'));
    //     }
    //     return array(
    //         'before' => $before,
    //         'after' => $after,
    //     );
    // }
    // private function getSales($div_id, $products, $brick, $dates)
    // {
    //     return round(Sale::select(
    //         'sales_details.value',
    //     )
    //         ->whereIntegerInRaw('sales.product_id', $products)
    //         ->where('sales_details.brick_id', $brick)
    //         ->where('sales_details.div_id', $div_id)
    //         ->whereIn(DB::raw("(DATE_FORMAT(crm_sales_details.date,'%Y-%m'))"), $dates)
    //         ->sum("sales_details.value"), 2);
    // }
    private function getSales($divId, $product_ids, $brickId, $months)
    {
        return round(DB::table('sales_details')
            ->join('sales', 'sales_details.sale_id', '=', 'sales.id')
            ->where('sales_details.brick_id', $brickId)
            ->where('sales_details.div_id', $divId)
            ->whereIn('sales.product_id', $product_ids)
            ->whereBetween('sales_details.date', $months)
            ->sum('sales_details.value'), 2);
    }
    /**
     * Get linked pharmacy sales for specific account, brand, brick, division and date range
     */
    private function getAvgSales($div_id, $products, $pharmacies, $months, $count)
    {
        $distinctSales = DB::table('mapping_sale')
            ->join('sales', 'mapping_sale.sale_id', 'sales.id')
            ->whereIntegerInRaw('mapping_sale.mapping_id', $pharmacies->toArray())
            ->whereIntegerInRaw('sales.product_id', $products)
            ->whereBetween('sales.date', $months)
            ->select('sales.id')
            ->distinct();

        return round(DB::table('sales_details')
            // ->where('sales_details.brick_id', $brickId)
            ->where('sales_details.div_id', $div_id)
            ->whereBetween('sales_details.date', $months)
            ->whereIn('sales_details.sale_id', $distinctSales)
            ->sum('sales_details.value') / $count, 2);
    }

    // private function getAvgSales($div_id, $products, $pharmacies, $dates)
    // {
    //     return round(SaleDetail::whereHas('sale', fn($join) => $join->whereIntegerInRaw('sales.product_id', $products))
    //         ->join('mapping_sale', fn($join) => $join->on('sales_details.sale_id', 'mapping_sale.sale_id')
    //             ->whereIntegerInRaw('mapping_sale.mapping_id', $pharmacies->toArray()))
    //         ->leftJoin('sales_details', 'sales.id', 'sales_details.sale_id')
    //         ->whereIn(DB::raw("(DATE_FORMAT(crm_sales_details.date,'%Y-%m'))"), $dates)
    //         ->sum("sales_details.value") / count($dates), 2);
    // }

    public function showData(Request $request)
    {
        // throw new CrmException($request->all());
        $commercial = CommercialRequest::find($request->id);
        $pharmacy_count = LinkedParmaciesSetting::where('key', 'number_of_linked_pharmacies')->value("value");
        $doctors = collect(CommercialDoctor::with(['doctor', 'account.pharmacies'])->where('request_id', $commercial->id)->get()
            ->map(function ($doctor) use ($commercial, $pharmacy_count) {
                // Fetch and process the pharmacies
                $pharmacies = collect($doctor->account->pharmacies()->whereNull('linked_pharmacies.deleted_at')->with('pharmable')->get()->pluck('pharmable')
                    ->map(function ($pharmable) {
                        return $pharmable->name . ' (' . $pharmable->code . ')';
                    }));

                // Prepare pharmacy columns, filling with null if there are fewer pharmacies
                $pharmacy_columns = [];
                for ($i = 1; $i <= $pharmacy_count; $i++) {
                    $pharmacy_columns['pharmacy_' . $i] = $pharmacies->get($i - 1) ?? '';  // Fill with pharmacy or null
                }

                // Return the doctor's data along with the dynamic pharmacy columns
                return array_merge([
                    'id' => $commercial?->id,
                    'type' => $commercial?->requestType?->name,
                    'user' => $commercial?->user?->fullname,
                    'doctor' => $doctor?->doctor?->name,
                    'ucode' => $doctor?->doctor?->ucode,
                    'brick' => $doctor?->doctor?->newaccountdoctors?->first()->accountLine?->brick?->name,
                ], $pharmacy_columns);
            }));
        $outOfList = collect(CommercialOutOfList::where('request_id', $commercial?->id)->get()->map(function ($doctor) use ($commercial) {
            return [
                'id' => $commercial?->id,
                'type' => $commercial?->requestType?->name,
                'user' => $commercial?->user?->fullname,
                'doctor' => $doctor?->name,
                'pharmacies' => 'Out Of List',
            ];
        }));
        $doctors = $doctors->merge($outOfList);
        return $this->respond($doctors);
    }
}

<?php

namespace App\Http\Controllers;

use App\ActualVisit;
use App\Exceptions\CrmException;
use App\Line;
use App\LineUser;
use App\Models\CheckLocation;
use App\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class EmployeeRouteController extends Controller
{
    public function index()
    {
        $lines = Line::select('lines.id', 'lines.name')->orderBy('lines.sort', 'ASC')->get();
        return response()->json(['lines' => $lines]);
    }
    public function getLineEmployees(Line $line)
    {
        /**@var User $auth */
        $auth = Auth::user();
        $users = $auth->belowUsersWithPositions($line);

        return response()->json(['users' => $users]);
    }
    public function employeeRoute(Request $request)
    {
        $checkLocation = CheckLocation::where('user_id', $request->user_id)->whereDate('checkin_date', $request->date)->get();
        $user = User::find($request->user_id);
        $managers = $user->allAboveUsers();

        $markers = collect();
        if (count($checkLocation) > 0) {
            $checkin = collect($checkLocation->map(function ($check) {
                return [
                    'id' => $check->id,
                    'account' => $check->user->fullname,
                    'doctor' => 'check-in',
                    'date' => Carbon::parse($check->checkin_date)->toDateTimeString(),
                    'position' => [
                        'id' => (float)$check->user_id,
                        'lat' => (float)$check->checkin_ll,
                        'lng' => (float)$check->checkin_lg,
                    ],
                ];
            }));

            $markers = $markers->merge($checkin);
        }
        $visits = collect(ActualVisit::where('line_id', $request->line_id)
            ->where('user_id', $request->user_id)
            ->whereDate('visit_date', $request->date)
            ->get()->map(function ($visit) {
                return [
                    'id' => $visit->id,
                    'account_id' => $visit->account?->id,
                    'account' => $visit->account?->name,
                    'doctor' => $visit->doctor?->name,
                    'doctor_id' => $visit->doctor?->id,
                    'managers' => $visit->actualVisitManagers->pluck('user_id'),
                    'date' => Carbon::parse($visit->visit_date)->toDateTimeString(),
                    "position" => [
                        'id' => $visit->id,
                        'lat' => (float)$visit->ll,
                        'lng' => (float)$visit->lg,
                    ],
                ];
            }));
        if (count($visits) > 0) {
            $markers = $markers->merge($visits);
        }
        if (count($checkLocation) > 0) {
            $checkout = collect($checkLocation->map(function ($check) {
                return [
                    'id' => $check->id,
                    'account' => $check->user->fullname,
                    'doctor' => 'check-out',
                    'date' => Carbon::parse($check->checkout_date)->toDateTimeString(),
                    'position' => [
                        'id' => (float)$check->user_id,
                        'lat' => (float)$check->checkout_ll,
                        'lng' => (float)$check->checkout_lg,
                    ],
                ];
            }));

            $markers = $markers->merge($checkout);
        }
        $dist = 0;
        if (count($markers) > 0) {
            $markerLatLngs = $markers->pluck('position')->filter(function ($marker) {
                return isset($marker['lat'], $marker['lng']) && ($marker['lat'] != 0 || $marker['lng'] != 0);
            })->map(function ($marker) {
                return [
                    'll' => $marker['lat'],
                    'lg' => $marker['lng']
                ];
            })->values();
            // throw new CrmException($markerLatLngs);
            $dist = round($this->routeDistance($markerLatLngs), 2);
        }

        $markerManagers = [];
        foreach ($managers as $manager) {
            $managerMarkers = collect();
            foreach ($visits as $visit) {
                $doubleVisits = collect();
                if ($visit['managers']->contains($manager->id)) {
                    $accountId = $visit['account_id'];
                    $doctorId = $visit['doctor_id'];
                    $doubleVisits = ActualVisit::where('account_id', $accountId)
                        ->whereDate('visit_date', $request->date)
                        ->where('account_dr_id', $doctorId)
                        ->where('user_id', $manager->id)
                        ->get()
                        ->map(function ($visit) use ($manager) {
                            return [
                                'id' => $visit->id,
                                'account' => $visit->account?->name,
                                'doctor' => $visit->doctor?->name,
                                'manager_id' => $manager->id,
                                'date' => Carbon::parse($visit->visit_date)->toDateTimeString(),
                                'position' => [
                                    'id' => $visit->id,
                                    'lat' => (float)$visit->ll,
                                    'lng' => (float)$visit->lg,
                                ],
                            ];
                        });
                    }
                    $managerMarkers = $managerMarkers->merge($doubleVisits);
            }

            // Save this manager's markers in the map
            $markerManagers["markerManager_" . $manager->id] = $managerMarkers;
        }


        return response()->json([
            'markers' => $markers,
            'markerManagers' => $markerManagers,
            'dist' => $dist
        ]);
    }

    private function routeDistance($positions, $unit = "K")
    {
        $length = $positions->count();
        $dist = 0.0;

        for ($i = 0; $i < $length - 1; $i++) {
            $dist += $this->distanceBetweenTwoCoordinates($positions[$i], $positions[$i + 1], $unit);
        }

        return $dist;
    }

    function distanceBetweenTwoCoordinates($p1, $p2, $unit)
    {
        $lat1 = (float)$p1['ll'];
        $lon1 = (float)$p1['lg'];
        $lat2 = (float)$p2['ll'];
        $lon2 = (float)$p2['lg'];
        if (($lat1 == $lat2) && ($lon1 == $lon2)) {
            return 0;
        } else {
            $theta = $lon1 - $lon2;
            $dist = sin(deg2rad($lat1)) * sin(deg2rad($lat2)) +  cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * cos(deg2rad($theta));
            $dist = acos($dist);
            $dist = rad2deg($dist);
            $miles = $dist * 60 * 1.1515;
            $unit = strtoupper($unit);

            if ($unit == "K")
                return ($miles * 1.609344 * 1.2);

            else if ($unit == "N")
                return ($miles * 0.8684);
            else if ($unit == "M")
                return ($miles * 1609.34);
            else
                return $miles;
        }
    }
}

<?php

namespace App\Http\Controllers;

use App\LinkedParmaciesSetting;
use App\Models\CommercialRequest\CommercialRequest;
use App\Models\CommercialRequest\CommercialDoctor;
use App\Models\CommercialRequest\CommercialProduct;
use App\Models\LinkedPharmacy;
use App\Sale;
use App\SaleDetail;
use App\Product;
use App\Brand;
use App\Line;
use App\LineDivision;
use App\Brick;
use App\User;
use App\Account;
use App\Doctor;
use App\Exceptions\CrmException;
use App\Helpers\LogActivity;
use App\Http\Requests\RoiTrendRequest;
use App\Mapping;
use Carbon\CarbonPeriod;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Carbon;

class RoiTrendController extends ApiController
{

    private function getCommercials($lineIds, $from, $to, $requestTypes)
    {
        return CommercialRequest::select([
            'commercial_requests.id as id',
            'commercial_requests.created_at as insertion_date',
            'commercial_requests.from_date as event_date',
            'commercial_requests.description as description',
            DB::raw('IFNULL(crm_commercial_requests.amount,"") as total_cost'),
            'request_types.name as type',
            'brands.id as brand_id',
            'brands.name as brand',
            'commercial_products.ratio as brand_ratio',
            DB::raw('IFNULL(GROUP_CONCAT(DISTINCT crm_brand_products.id), "") as brand_product_ids'),
            DB::raw('IFNULL(GROUP_CONCAT(DISTINCT crm_linked_pharmacies.pharmable_id), "") as pharmacies_ids'),
            'accounts.id as account_id',
            'accounts.name as account',
            'doctors.id as doctor_id',
            'doctors.name as doctor',
            'lines.id as line_id',
            'lines.name as line',
            'line_divisions.id as division_id',
            'line_divisions.name as division',
            'bricks.id as brick_id',
            'bricks.name as brick',
            DB::raw('IFNULL(GROUP_CONCAT(DISTINCT crm_users.id), "") as employee_id'),
            DB::raw('IFNULL(GROUP_CONCAT(DISTINCT crm_users.fullname), "") as employee'),
            DB::raw('
    (
        SELECT COUNT(DISTINCT cd.id)
        FROM crm_commercial_doctors cd
        WHERE cd.request_id = crm_commercial_requests.id
    ) +
    (
        SELECT COUNT(DISTINCT col.id)
        FROM crm_commercial_out_of_lists col
        WHERE col.request_id = crm_commercial_requests.id
    )
    as total_docs
'),
            // DB::raw('COUNT(distinct crm_commercial_doctors.id) + COUNT(distinct crm_commercial_out_of_lists.id)  as total_docs')
            // DB::raw('IFNULL(GROUP_CONCAT(DISTINCT crm_bricks.id), "") as brick_id'),
            // DB::raw('IFNULL(GROUP_CONCAT(DISTINCT crm_bricks.name), "") as brick'),
            // DB::raw('IFNULL(GROUP_CONCAT(DISTINCT crm_line_divisions.id), "") as division_id'),
            // DB::raw('IFNULL(GROUP_CONCAT(DISTINCT crm_line_divisions.name), "") as division'),
            // DB::raw('IFNULL(GROUP_CONCAT(DISTINCT crm_lines.id), "") as line_id'),
            // DB::raw('IFNULL(GROUP_CONCAT(DISTINCT crm_lines.name), "") as line'),
        ])
            ->leftJoin('commercial_divisions', 'commercial_requests.id', 'commercial_divisions.request_id')
            ->leftJoin('request_types', 'commercial_requests.request_type_id', 'request_types.id')
            ->join('commercial_doctors', 'commercial_requests.id', 'commercial_doctors.request_id')
            ->join('doctors', 'commercial_doctors.doctor_id', 'doctors.id')
            ->join('commercial_products', 'commercial_requests.id', 'commercial_products.request_id')
            ->join('products', 'commercial_products.product_id', 'products.id')
            ->leftJoin(
                'product_brands',
                fn($join) =>
                $join->on('products.id', '=', 'product_brands.product_id')
                    ->where('product_brands.from_date', '<=', $from->toDateString())
                    ->where(
                        fn($q) =>
                        $q->whereNull('product_brands.to_date')->orWhere('product_brands.to_date', '>=', $to->toDateString())
                    )
            )
            ->leftJoin('brands', 'product_brands.brand_id', 'brands.id')
            // join all brand products, even if not used in commercial_products
            ->leftJoin(
                'product_brands as brand_products_map',
                fn($join) =>
                $join->on('brand_products_map.brand_id', '=', 'product_brands.brand_id')
                    ->where('brand_products_map.from_date', '<=', $from->toDateString())
                    ->where(
                        fn($q) =>
                        $q->whereNull('brand_products_map.to_date')
                            ->orWhere('brand_products_map.to_date', '>=', $to->toDateString())
                    )
            )
            ->leftJoin('products as brand_products', 'brand_products_map.product_id', 'brand_products.id')
            ->leftJoin('accounts', 'commercial_doctors.account_id', 'accounts.id')
            ->leftJoin(
                'account_lines',
                fn($join) =>
                $join->on('accounts.id', '=', 'account_lines.account_id')
                    ->where('account_lines.from_date', '<=', $from->toDateString())
                    ->whereIntegerInRaw('account_lines.line_id', $lineIds)
                    ->where(
                        fn($q) =>
                        $q->whereNull('account_lines.to_date')->orWhere('account_lines.to_date', '>=', $to->toDateString())
                    )
            )
            ->leftJoin(
                'line_products',
                fn($join) =>
                $join->on('products.id', '=', 'line_products.product_id')
                    ->where('line_products.from_date', '<=', $from->toDateString())
                    ->whereIntegerInRaw('line_products.line_id', $lineIds)
                    ->where(
                        fn($q) =>
                        $q->whereNull('line_products.to_date')->orWhere('line_products.to_date', '>=', $to->toDateString())
                    )
            )
            ->join(
                'lines',
                fn($join) => $join
                    ->on('account_lines.line_id', 'lines.id')
                    ->on('line_products.line_id', 'lines.id')
            )
            ->join(
                'line_divisions',
                fn($join) => $join
                    // ->on('commercial_divisions.div_id', 'line_divisions.id')
                    ->on('account_lines.line_division_id', 'line_divisions.id')
                    ->on('lines.id', 'line_divisions.line_id')
                    ->where('line_divisions.is_kol', 0)
                    ->where('line_divisions.from_date', '<=', $from->toDateString())
                    ->whereNull('line_divisions.deleted_at')
                    ->where(fn($q) => $q->where('line_divisions.to_date', '=', null)
                        ->orWhere('line_divisions.to_date', '>=', $to->toDateString()))
            )
            ->leftJoin(
                'line_users_divisions',
                fn($join) =>
                $join->on('line_divisions.id', 'line_users_divisions.line_division_id')
                    ->where('line_users_divisions.from_date', '<=', $from->toDateString())
                    ->whereNull('line_users_divisions.deleted_at')
                    ->where(fn($q) => $q->where('line_users_divisions.to_date', '=', null)
                        ->orWhere('line_users_divisions.to_date', '>=', $to->toDateString()))
            )
            ->leftJoin('users', 'line_users_divisions.user_id', 'users.id')
            ->leftJoin('bricks', 'account_lines.brick_id', 'bricks.id')
            ->leftjoin('linked_pharmacies', fn($join) => $join->on('accounts.id', 'linked_pharmacies.account_id')
                ->whereNull('linked_pharmacies.deleted_at'))
            ->whereBetween('commercial_requests.created_at', [$from, $to])
            ->whereIntegerInRaw('commercial_requests.request_type_id', $requestTypes)
            ->groupBy(
                'commercial_requests.id',
                'brands.id',
                'accounts.id',
                'request_types.name',
                'brands.name',
                'commercial_products.ratio',
                'accounts.name',
                'doctors.id',
                'doctors.name',
                'lines.id',
                'lines.name',
                'line_divisions.id',
                'line_divisions.name',
                'bricks.id',
                'bricks.name',
            )
            ->get();
    }

    /**
     * Generate ROI Trend Report
     */
    public function generateReport(RoiTrendRequest $request)
    {
        $roiFilter = $request->roiFilter;
        $from = Carbon::parse($roiFilter['fromDate'])->startOfDay();
        $to = Carbon::parse($roiFilter['toDate'])->endOfDay();
        $year = Carbon::parse($from)->format('Y');
        $months = [];
        $period = CarbonPeriod::create($from, '1 month', $to);
        foreach ($period as $date) {
            $months[] = $date->format('F'); // 'F' gives full month name, use 'm' for month number
        }
        $dates[] = [
            'Month' => implode(', ', $months),
            'Year' => $year,
        ];
        // Get LinkedPharmaciesSetting values
        $monthsBefore = LinkedParmaciesSetting::where('key', 'number_of_month_befor')->value('value') ?? 2;
        $monthsAfter = LinkedParmaciesSetting::where('key', 'number_of_month_after')->value('value') ?? 3;
        // Build commercial requests query
        $commercialRequests = $this->getCommercials($roiFilter['lines'], $from, $to, $roiFilter['types']);


        $fields = [
            'id',
            'total_docs',
            'insertion_date',
            'event_date',
            'description',
            'type',
            'account',
            'account_id',
            'doctor',
            'doctor_id',
            'line',
            'division',
            'brick',
            'employee',
            'brand',
            'brand_ratio',
            'total_cost',
            'cost_per_doc',
            // 'brand_product_ids',
            'brick_sales_before',
            // 'brick_sales_factor',
            'linked_pharm_before',
            // 'linked_pharm_factor'
        ];

        // Add dynamic month columns
        for ($i = 1; $i <= $monthsAfter; $i++) {
            $fields[] = "brick_sales_month_{$i}";
            // $fields[] = "brick_sales_arrow_{$i}";
            $fields[] = "linked_pharm_month_{$i}";
            // $fields[] = "linked_pharm_arrow_{$i}";
        }

        $fields = array_merge($fields, [
            'total_brick_sales_after',
            'total_linked_pharm_after',
            'brick_sales_trend',
            'linked_pharm_trend'
        ]);


        foreach ($commercialRequests as $commercialRequest) {

            $commercialRequest->cost_per_doc = $commercialRequest->total_docs > 0
                ? round(($commercialRequest->total_cost * $commercialRequest->brand_ratio / 100) / $commercialRequest->total_docs, 2) : 0;
            $commercialRequest->brand_ratio = $commercialRequest->brand_ratio . " %";
            $productIds = explode(',', $commercialRequest->brand_product_ids);
            $linkedPharmacies = explode(',', $commercialRequest->pharmacies_ids);
            $brickId = $commercialRequest->brick_id;
            $divId = $commercialRequest->division_id;
            $monthsBeforeArray =  [
                Carbon::parse($commercialRequest->insertion_date)->subMonth($monthsBefore)->startOfMonth()->toDateString(),
                Carbon::parse($commercialRequest->insertion_date)->subMonth()->endOfMonth()->toDateString()
            ];
            // $monthsAfterArray =  [
            //     Carbon::parse($commercialRequest->insertion_date)->addMonth()->startOfMonth()->toDateString(),
            //     Carbon::parse($commercialRequest->insertion_date)->addMonth($monthsAfter)->endOfMonth()->toDateString()
            // ];

            // Calculate before period sales
            $brickSalesBefore = round($this->getBrickSales($brickId, $divId, $productIds, $monthsBeforeArray), 2);
            $commercialRequest->brick_sales_before = $brickSalesBefore;


            // Calculate brick Sales factors
            $commercialRequest->brick_sales_factor = $monthsBefore > 0 ?
                round($commercialRequest->brick_sales_before / $monthsBefore, 2)
                : 0;

            // Calculate linked pharmacy sales before
            $linkedPharmBefore = round($this->getLinkedPharmSales($linkedPharmacies, $productIds, $brickId, $divId, $monthsBeforeArray), 2);
            $commercialRequest->linked_pharm_before = $linkedPharmBefore;


            // Calculate linked pharmacy factors
            $commercialRequest->linked_pharm_factor = $monthsBefore > 0 ?
                round($commercialRequest->linked_pharm_before / $monthsBefore, 2)
                : 0;


            // Calculate after period sales for each month
            $totalBrickSalesAfter = 0;
            $totalLinkedPharmAfter = 0;
            for ($i = 1; $i <= $monthsAfter; $i++) {
                $monthArray = [
                    Carbon::parse($commercialRequest->insertion_date)->addMonths($i)->startOfMonth(),
                    Carbon::parse($commercialRequest->insertion_date)->addMonths($i)->endOfMonth()
                ];

                $brickSalesMonth = round($this->getBrickSales($brickId, $divId, $productIds, $monthArray), 2);
                $linkedPharmMonth = round($this->getLinkedPharmSales($linkedPharmacies, $productIds, $brickId, $divId, $monthArray), 2);

                $totalBrickSalesAfter += $brickSalesMonth;
                $totalLinkedPharmAfter += $linkedPharmMonth;

                // Calculate arrows (trend indicators)
                $brickArrow = $this->getTrendArrow($brickSalesMonth, $commercialRequest->brick_sales_factor);
                $linkedPharmArrow = $this->getTrendArrow($linkedPharmMonth, $commercialRequest->linked_pharm_factor);

                $commercialRequest["brick_sales_month_{$i}"] = number_format($brickSalesMonth, 2);
                $commercialRequest["brick_sales_arrow_{$i}"] = $brickArrow;
                $commercialRequest["linked_pharm_month_{$i}"] = number_format($linkedPharmMonth, 2);
                $commercialRequest["linked_pharm_arrow_{$i}"] = $linkedPharmArrow;
            }

            $commercialRequest->total_brick_sales_after = round($totalBrickSalesAfter, 2);
            $commercialRequest->total_linked_pharm_after = round($totalLinkedPharmAfter, 2);
            $commercialRequest['brick_sales_trend'] = $this->getTrendArrow($totalBrickSalesAfter, $brickSalesBefore);
            $commercialRequest['linked_pharm_trend'] = $this->getTrendArrow($totalLinkedPharmAfter, $linkedPharmBefore);
            // $commercialRequest->total_brick_sales_after = round($this->getBrickSales($brickId, $divId, $productIds, $monthsAfterArray), 2);
            // $commercialRequest->total_linked_pharm_after = round($this->getLinkedPharmSales($linkedPharmacies, $productIds, $brickId, $divId, $monthsAfterArray), 2);
        }
        // throw new CrmException($commercialRequests);

        return response()->json([
            'dates' => $dates,
            'data' => $commercialRequests,
            'fields' => $fields,
        ]);
    }

    /**
     * Get brick sales for specific brand, brick, division and date range
     */
    private function getBrickSales($brickId, $divId, $product_ids, $months)
    {
        return DB::table('sales_details')
            ->join('sales', 'sales_details.sale_id', '=', 'sales.id')
            ->where('sales_details.brick_id', $brickId)
            ->where('sales_details.div_id', $divId)
            ->whereIn('sales.product_id', $product_ids)
            ->whereBetween('sales_details.date', $months)
            ->sum('sales_details.value');
    }

    /**
     * Get linked pharmacy sales for specific account, brand, brick, division and date range
     */
    private function getLinkedPharmSales($pharmacies_ids, $product_ids, $brickId, $divId, $months)
    {
        $distinctSales = DB::table('mapping_sale')
            ->join('sales', 'mapping_sale.sale_id', 'sales.id')
            ->whereIntegerInRaw('mapping_sale.mapping_id', $pharmacies_ids)
            ->whereIntegerInRaw('sales.product_id', $product_ids)
            ->whereBetween('sales.date', $months)
            ->select('sales.id')
            ->distinct();

        return DB::table('sales_details')
            ->where('sales_details.brick_id', $brickId)
            ->where('sales_details.div_id', $divId)
            ->whereBetween('sales_details.date', $months)
            ->whereIn('sales_details.sale_id', $distinctSales)
            ->sum('sales_details.value');
    }

    /**
     * Get trend arrow based on comparison
     */
    private function getTrendArrow($currentValue, $compareValue)
    {
        if ($compareValue == 0) {
            return $currentValue > 0 ? '↑' : '→';
        }

        $percentage = (($currentValue - $compareValue) / $compareValue) * 100;

        if ($percentage > 5) {
            return '↑'; // Up arrow for significant increase
        } elseif ($percentage < -5) {
            return '↓'; // Down arrow for significant decrease
        } else {
            return '→'; // Right arrow for stable/minor change
        }
    }
}
